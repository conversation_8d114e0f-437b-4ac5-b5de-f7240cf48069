<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Interactive Proposal: Credit Department Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- Chosen Palette: Warm Neutrals (Slate, Stone, Zinc) with Amber Accent -->
    <!-- Application Structure Plan: The SPA is designed as a single-page dashboard. The structure prioritizes user understanding by breaking down the complex proposal into logical, interactive sections: 1. Header with key metrics (Budget, Timeline) for an immediate overview. 2. Navigation bar to allow quick jumps to different aspects of the proposal (Vision, Modules, Timeline, Financials). 3. A 'Project Vision' section to set the context. 4. An interactive 'Core Modules' section where users can click on each module to see its details, cost, and timeline, which is more engaging than a static table. 5. A 'Project Timeline' section with a Gantt-chart-like visualization to clearly show the 12-week plan phase by phase. 6. A 'Financial Overview' with interactive charts comparing the original vs. revised budget, highlighting the value of the new strategy. This structure guides the user from a high-level summary down to the details in a controlled, intuitive flow, making the dense information of the report digestible and easy to explore. -->
    <!-- Visualization & Content Choices: 
        - Report Info: Total Budget & Timeline -> Goal: Inform -> Viz/Method: Large stat cards in the header -> Interaction: Static display -> Justification: Provides immediate, high-level project constraints.
        - Report Info: Module descriptions, costs, hours -> Goal: Organize & Compare -> Viz/Method: Interactive grid of clickable cards (HTML/CSS/JS) -> Interaction: On-click, a modal or detail pane reveals the full description, cost, and savings for that module. -> Justification: More engaging than a long table; allows users to focus on one module at a time. Library/Method: Vanilla JS + Tailwind CSS.
        - Report Info: 12-Week Phased Plan -> Goal: Show Change/Process -> Viz/Method: A horizontal timeline visualization built with HTML/CSS Grid. -> Interaction: Hovering over a week/phase could highlight details. -> Justification: A Gantt-like view is the standard and most intuitive way to represent project timelines. Library/Method: HTML/CSS Grid + JS for interactivity.
        - Report Info: Budget Breakdown (Original vs. Revised) -> Goal: Compare & Justify -> Viz/Method: A grouped bar chart (Chart.js) to show cost differences per module and a donut chart to show the new budget allocation. -> Interaction: Tooltips on hover provide exact figures. -> Justification: Bar charts are excellent for direct comparison, and a donut chart clearly shows the composition of the new budget. Library/Method: Chart.js (Canvas).
        - Report Info: Payment Schedule -> Goal: Inform -> Viz/Method: A simple, clear table. -> Interaction: None. -> Justification: A table is the clearest way to present structured financial milestones. Library/Method: HTML/Tailwind.
        - CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
      body {
        font-family: "Inter", sans-serif;
        background-color: #f8fafc; /* slate-50 */
      }
      .chart-container {
        position: relative;
        width: 100%;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        height: 350px;
        max-height: 400px;
      }
      @media (min-width: 768px) {
        .chart-container {
          height: 400px;
        }
      }
      .nav-link {
        transition: all 0.3s ease;
        cursor: pointer;
      }
      .nav-link.active {
        color: #f59e0b; /* amber-500 */
        border-bottom-color: #f59e0b;
      }
      .module-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .module-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),
          0 4px 6px -4px rgb(0 0 0 / 0.1);
      }
      .timeline-bar {
        transition: all 0.3s ease;
      }
      .timeline-phase-1 {
        background-color: #38bdf8;
      } /* sky-400 */
      .timeline-phase-2 {
        background-color: #34d399;
      } /* emerald-400 */
      .timeline-phase-3 {
        background-color: #fbbf24;
      } /* amber-400 */
      .timeline-milestone {
        background-color: #ef4444;
      } /* red-500 */
    </style>
  </head>
  <body class="bg-slate-50 text-slate-800">
    <div class="container mx-auto p-4 sm:p-6 lg:p-8">
      <!-- Header -->
      <header class="text-center mb-8">
        <h1 class="text-3xl md:text-4xl font-bold text-slate-900">
          Credit Department Management System
        </h1>
        <p class="text-lg text-slate-600 mt-2">
          An Interactive Project Proposal
        </p>
        <div
          class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto"
        >
          <div
            class="bg-white p-4 rounded-lg shadow-md border border-slate-200"
          >
            <p class="text-sm text-slate-500">Total Investment</p>
            <p class="text-2xl font-semibold text-slate-800">3,00,000 INR</p>
          </div>
          <div
            class="bg-white p-4 rounded-lg shadow-md border border-slate-200"
          >
            <p class="text-sm text-slate-500">Project Timeline</p>
            <p class="text-2xl font-semibold text-slate-800">
              3 Months (12 Weeks)
            </p>
          </div>
          <div
            class="bg-white p-4 rounded-lg shadow-md border border-slate-200"
          >
            <p class="text-sm text-slate-500">Total Savings</p>
            <p class="text-2xl font-semibold text-emerald-600">1,20,000 INR</p>
          </div>
        </div>
      </header>

      <!-- Navigation -->
      <nav
        class="sticky top-0 bg-white/80 backdrop-blur-md z-10 shadow-sm rounded-lg mb-8"
      >
        <div class="max-w-6xl mx-auto px-4">
          <div class="flex justify-center items-center h-16">
            <div class="flex space-x-6 md:space-x-8 text-slate-600">
              <a
                href="#vision"
                class="nav-link border-b-2 border-transparent pb-1"
                >Project Vision</a
              >
              <a
                href="#modules"
                class="nav-link border-b-2 border-transparent pb-1"
                >Core Modules</a
              >
              <a
                href="#timeline"
                class="nav-link border-b-2 border-transparent pb-1"
                >Timeline</a
              >
              <a
                href="#financials"
                class="nav-link border-b-2 border-transparent pb-1"
                >Financials</a
              >
              <a
                href="#roadmap"
                class="nav-link border-b-2 border-transparent pb-1"
                >Future Roadmap</a
              >
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <main>
        <!-- Project Vision Section -->
        <section id="vision" class="mb-12 scroll-mt-24">
          <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold text-slate-900 mb-4 text-center">
              Efficiency Through Intelligent Integration
            </h2>
            <p class="text-slate-700 leading-relaxed">
              This project's core vision is to deliver a high-quality, robust,
              and scalable Credit Department Management System (CDMS) within the
              client's revised budget of 3,00,000 INR and a 3-month timeline. We
              achieve this not by cutting corners, but by adopting a smarter
              engineering strategy. Instead of building every component from
              scratch, we will intelligently integrate powerful, secure, and
              proven open-source technologies. This strategic pivot de-risks the
              project, reduces costs, and accelerates development, allowing us
              to focus on tailoring the core business logic to your specific
              needs. The result is a feature-rich, enterprise-grade system that
              provides immediate value and a solid foundation for future growth.
            </p>
          </div>
        </section>

        <!-- Core Modules Section -->
        <section id="modules" class="mb-12 scroll-mt-24">
          <h2 class="text-2xl font-bold text-slate-900 mb-6 text-center">
            Core Modules &amp; Strategic Pivots
          </h2>
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            id="module-grid"
          >
            <!-- Module cards will be injected here by JS -->
          </div>
          <div
            id="module-details"
            class="mt-6 bg-white p-6 rounded-lg shadow-inner border border-slate-200 hidden"
          >
            <!-- Details of selected module will be shown here -->
          </div>
        </section>

        <!-- Project Timeline Section -->
        <section id="timeline" class="mb-12 scroll-mt-24">
          <h2 class="text-2xl font-bold text-slate-900 mb-6 text-center">
            12-Week Project Timeline
          </h2>
          <div class="bg-white p-6 rounded-lg shadow-lg overflow-x-auto">
            <p class="text-slate-700 mb-6 leading-relaxed">
              The project is structured into three distinct 4-week phases,
              ensuring a continuous flow of delivery and clear milestones. This
              phased approach allows for regular feedback and ensures the most
              critical functionality is delivered first. Hover over each week to
              see the primary focus.
            </p>
            <div class="flex items-center mb-2 space-x-4 text-sm">
              <div class="flex items-center">
                <span class="w-4 h-4 rounded-full timeline-phase-1 mr-2"></span
                >Phase 1: Foundation
              </div>
              <div class="flex items-center">
                <span class="w-4 h-4 rounded-full timeline-phase-2 mr-2"></span
                >Phase 2: Business Logic
              </div>
              <div class="flex items-center">
                <span class="w-4 h-4 rounded-full timeline-phase-3 mr-2"></span
                >Phase 3: Integration & Polish
              </div>
              <div class="flex items-center">
                <span
                  class="w-4 h-4 rounded-full timeline-milestone mr-2"
                ></span
                >Milestone
              </div>
            </div>
            <div
              class="grid grid-cols-12 gap-1 text-center text-xs font-medium text-slate-600"
            >
              <!-- Weeks 1-12 headers -->
              <div class="p-2">W1</div>
              <div class="p-2">W2</div>
              <div class="p-2">W3</div>
              <div class="p-2">W4</div>
              <div class="p-2">W5</div>
              <div class="p-2">W6</div>
              <div class="p-2">W7</div>
              <div class="p-2">W8</div>
              <div class="p-2">W9</div>
              <div class="p-2">W10</div>
              <div class="p-2">W11</div>
              <div class="p-2">W12</div>
            </div>
            <div id="timeline-grid" class="grid grid-cols-12 gap-1 mt-2">
              <!-- Timeline bars will be injected here by JS -->
            </div>
          </div>
        </section>

        <!-- Financials Section -->
        <section id="financials" class="mb-12 scroll-mt-24">
          <h2 class="text-2xl font-bold text-slate-900 mb-6 text-center">
            Financial Overview & Cost Optimization
          </h2>
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div class="bg-white p-6 rounded-lg shadow-lg">
              <h3 class="text-xl font-semibold text-slate-800 mb-4 text-center">
                Budget Breakdown: Original vs. Revised
              </h3>
              <p class="text-slate-700 mb-4 text-sm leading-relaxed">
                This chart illustrates the significant cost savings achieved per
                module through our strategic pivot to open-source integration
                and efficient development. The most dramatic reductions are in
                the Risk Engine and Document Management System, delivering
                superior functionality for a fraction of the original cost.
              </p>
              <div class="chart-container">
                <canvas id="budget-chart"></canvas>
              </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg">
              <h3 class="text-xl font-semibold text-slate-800 mb-4 text-center">
                Revised Budget Allocation
              </h3>
              <p class="text-slate-700 mb-4 text-sm leading-relaxed">
                This chart shows how the new 3,00,000 INR budget is allocated
                across the core project activities. It reflects a balanced
                investment in building custom workflows while leveraging
                technology for efficiency.
              </p>
              <div class="chart-container">
                <canvas id="allocation-chart"></canvas>
              </div>
            </div>
          </div>
          <div class="mt-8 bg-white p-6 rounded-lg shadow-lg">
            <h3 class="text-xl font-semibold text-slate-800 mb-4 text-center">
              Payment Schedule
            </h3>
            <p
              class="text-slate-700 mb-4 text-sm leading-relaxed text-center max-w-2xl mx-auto"
            >
              Payments are tied to tangible project milestones, ensuring a
              transparent and fair financial arrangement. Each payment is due
              upon the successful delivery and client acceptance of the
              corresponding milestone.
            </p>
            <div class="overflow-x-auto">
              <table class="w-full text-left">
                <thead class="bg-slate-100 text-slate-600">
                  <tr>
                    <th class="p-3">Milestone</th>
                    <th class="p-3">Deliverable</th>
                    <th class="p-3">Amount (INR)</th>
                    <th class="p-3">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="border-b border-slate-200">
                    <td class="p-3 font-semibold">Advance Payment</td>
                    <td class="p-3">Upon Project Commencement</td>
                    <td class="p-3">90,000</td>
                    <td class="p-3">30%</td>
                  </tr>
                  <tr class="border-b border-slate-200">
                    <td class="p-3 font-semibold">Midway Payment</td>
                    <td class="p-3">
                      Acceptance of Milestone 2 (End of Month 2)
                    </td>
                    <td class="p-3">1,20,000</td>
                    <td class="p-3">40%</td>
                  </tr>
                  <tr>
                    <td class="p-3 font-semibold">Final Payment</td>
                    <td class="p-3">
                      Final Deployment & Handover (End of Month 3)
                    </td>
                    <td class="p-3">90,000</td>
                    <td class="p-3">30%</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Future Roadmap Section -->
        <section id="roadmap" class="scroll-mt-24">
          <div
            class="bg-gradient-to-r from-slate-800 to-slate-900 text-white p-8 rounded-lg shadow-2xl"
          >
            <h2 class="text-2xl font-bold mb-4 text-center">
              Phase 2 & Beyond: A Strategic Roadmap
            </h2>
            <p
              class="text-slate-300 leading-relaxed text-center max-w-3xl mx-auto mb-6"
            >
              This project is the foundation for a long-term technology
              partnership. The chosen architecture is built for growth, allowing
              for seamless integration of future enhancements. This roadmap
              outlines potential next steps to further increase efficiency,
              intelligence, and customer engagement.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-slate-300">
              <div class="bg-slate-800 p-4 rounded-md">
                <h4 class="font-semibold text-amber-400">
                  AI/ML-Powered Risk Engine
                </h4>
                <p class="text-sm">
                  Train a model on historical data to identify subtle risk
                  patterns and score applicants with limited credit history.
                </p>
              </div>
              <div class="bg-slate-800 p-4 rounded-md">
                <h4 class="font-semibold text-amber-400">
                  Advanced Predictive Analytics
                </h4>
                <p class="text-sm">
                  Forecast default rates and identify portfolio-level trends to
                  inform future lending strategies.
                </p>
              </div>
              <div class="bg-slate-800 p-4 rounded-md">
                <h4 class="font-semibold text-amber-400">
                  Mobile App for Sales Team
                </h4>
                <p class="text-sm">
                  Empower agents to initiate applications and scan documents
                  directly from the field.
                </p>
              </div>
              <div class="bg-slate-800 p-4 rounded-md">
                <h4 class="font-semibold text-amber-400">
                  Customer Self-Service Portal
                </h4>
                <p class="text-sm">
                  Allow customers to view statements, track repayments, and make
                  payments online.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const moduleData = [
          {
            id: 1,
            title: "Authentication & Roles",
            originalCost: 46000,
            revisedCost: 40000,
            hours: 64,
            justification:
              "Streamlined implementation using MERN stack best practices and libraries like Passport.js.",
            pivot: false,
          },
          {
            id: 2,
            title: "Application Workflow",
            originalCost: 75000,
            revisedCost: 60000,
            hours: 96,
            justification:
              "Focused on core workflow functionality, deferring non-essential UI flourishes to a future phase.",
            pivot: false,
          },
          {
            id: 3,
            title: "Risk Analysis Engine",
            originalCost: 82000,
            revisedCost: 35000,
            hours: 56,
            justification:
              "Replaced complex, high-cost custom build with a highly efficient, auditable Rule-Based Weighted Scoring Engine for Phase 1.",
            pivot: true,
          },
          {
            id: 4,
            title: "Document Management",
            originalCost: 51000,
            revisedCost: 20000,
            hours: 32,
            justification:
              "Replaced costly custom development with rapid integration of feature-rich open-source Paperless-ngx.",
            pivot: true,
          },
          {
            id: 5,
            title: "Loan Lifecycle Flows",
            originalCost: 77000,
            revisedCost: 65000,
            hours: 104,
            justification:
              "Focused development on core modification logic (tenure, EMI) and automated agreement generation.",
            pivot: false,
          },
          {
            id: 6,
            title: "Email & Notifications",
            originalCost: 18000,
            revisedCost: 15000,
            hours: 24,
            justification:
              "Using a more cost-effective, high-deliverability transactional email provider like SMTP2GO or MailerSend.",
            pivot: false,
          },
          {
            id: 7,
            title: "Admin Dashboard",
            originalCost: 46000,
            revisedCost: 35000,
            hours: 56,
            justification:
              "Utilized pre-built, open-source charting libraries (e.g., Chart.js) for faster development.",
            pivot: false,
          },
          {
            id: 8,
            title: "Hosting & Deployment",
            originalCost: 23000,
            revisedCost: 20000,
            hours: 32,
            justification:
              "Standard cloud VPS deployment with CI/CD automation for efficiency.",
            pivot: false,
          },
        ];

        const timelineData = [
          {
            task: "Project Kick-off, Setup",
            start: 1,
            end: 1,
            phase: 1,
            info: "Finalize requirements, Tech Stack Setup, CI/CD",
          },
          {
            task: "Authentication & Roles",
            start: 2,
            end: 2,
            phase: 1,
            info: "Backend & Frontend for secure login",
          },
          {
            task: "Application Form & API",
            start: 3,
            end: 3,
            phase: 1,
            info: "Frontend form and backend endpoints",
          },
          {
            task: "DMS Integration",
            start: 4,
            end: 4,
            phase: 1,
            info: "Install, configure, and integrate Paperless-ngx",
          },
          {
            task: "Milestone 1",
            start: 5,
            end: 5,
            phase: "milestone",
            info: "Core Application Ready",
          },
          {
            task: "Risk Engine Logic",
            start: 5,
            end: 5,
            phase: 2,
            info: "Backend logic for rule-based scoring",
          },
          {
            task: "Risk Engine Integration",
            start: 6,
            end: 6,
            phase: 2,
            info: "Connect engine to application flow",
          },
          {
            task: "Reschedule/Settle Logic",
            start: 7,
            end: 7,
            phase: 2,
            info: "Backend logic for loan modifications",
          },
          {
            task: "Reschedule/Settle UI",
            start: 8,
            end: 8,
            phase: 2,
            info: "Frontend interface for officers",
          },
          {
            task: "Milestone 2",
            start: 9,
            end: 9,
            phase: "milestone",
            info: "All Core Workflows Complete",
          },
          {
            task: "Email System",
            start: 9,
            end: 9,
            phase: 3,
            info: "Integrate SMTP provider and create templates",
          },
          {
            task: "Admin Dashboard",
            start: 10,
            end: 10,
            phase: 3,
            info: "Develop metrics and charting",
          },
          {
            task: "End-to-End Testing",
            start: 11,
            end: 11,
            phase: 3,
            info: "Comprehensive testing and bug fixing",
          },
          {
            task: "UAT & Deployment",
            start: 12,
            end: 12,
            phase: 3,
            info: "User Acceptance Testing and production deployment",
          },
        ];

        // --- Module Cards Interaction ---
        const moduleGrid = document.getElementById("module-grid");
        const moduleDetails = document.getElementById("module-details");

        moduleData.forEach((module) => {
          const card = document.createElement("div");
          card.className =
            "module-card bg-white p-4 rounded-lg shadow-md border border-slate-200 cursor-pointer";
          card.dataset.id = module.id;
          let pivotHTML = module.pivot
            ? `<span class="absolute top-2 right-2 text-xs bg-amber-100 text-amber-800 font-semibold px-2 py-1 rounded-full">Strategic Pivot</span>`
            : "";
          card.innerHTML = `
                    <div class="relative">
                        <h3 class="font-semibold text-slate-800">${
                          module.title
                        }</h3>
                        <p class="text-sm text-slate-500 mt-1">Cost: ${module.revisedCost.toLocaleString(
                          "en-IN"
                        )} INR</p>
                        ${pivotHTML}
                    </div>
                `;
          moduleGrid.appendChild(card);
        });

        moduleGrid.addEventListener("click", function (e) {
          const card = e.target.closest(".module-card");
          if (card) {
            const moduleId = parseInt(card.dataset.id);
            const module = moduleData.find((m) => m.id === moduleId);
            const savings = module.originalCost - module.revisedCost;

            moduleDetails.innerHTML = `
                        <h4 class="font-bold text-xl text-slate-900 mb-2">${
                          module.title
                        }</h4>
                        <p class="text-slate-700 mb-4">${
                          module.justification
                        }</p>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                            <div>
                                <p class="text-sm text-slate-500">Revised Cost</p>
                                <p class="font-semibold text-lg">${module.revisedCost.toLocaleString(
                                  "en-IN"
                                )} INR</p>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Original Cost</p>
                                <p class="font-semibold text-lg line-through">${module.originalCost.toLocaleString(
                                  "en-IN"
                                )} INR</p>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Savings</p>
                                <p class="font-semibold text-lg text-emerald-600">${savings.toLocaleString(
                                  "en-IN"
                                )} INR</p>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Est. Hours</p>
                                <p class="font-semibold text-lg">${
                                  module.hours
                                }</p>
                            </div>
                        </div>
                    `;
            moduleDetails.classList.remove("hidden");

            // Highlight selected card
            document
              .querySelectorAll(".module-card")
              .forEach((c) => c.classList.remove("ring-2", "ring-amber-500"));
            card.classList.add("ring-2", "ring-amber-500");
          }
        });

        // --- Timeline Visualization ---
        const timelineGrid = document.getElementById("timeline-grid");
        timelineData.forEach((item) => {
          const bar = document.createElement("div");
          bar.className = `timeline-bar h-8 rounded flex items-center justify-center text-white text-xs p-1`;
          bar.style.gridColumn = `${item.start} / ${item.end + 1}`;
          bar.classList.add(
            item.phase === "milestone"
              ? "timeline-milestone"
              : `timeline-phase-${item.phase}`
          );
          bar.title = item.info;
          bar.innerHTML = `<span>${item.task}</span>`;
          timelineGrid.appendChild(bar);
        });

        // --- Chart.js Visualizations ---
        const budgetCtx = document
          .getElementById("budget-chart")
          .getContext("2d");
        new Chart(budgetCtx, {
          type: "bar",
          data: {
            labels: moduleData.map((m) => m.title.replace(" ", "\n")),
            datasets: [
              {
                label: "Original Cost (INR)",
                data: moduleData.map((m) => m.originalCost),
                backgroundColor: "#d1d5db", // gray-300
                borderColor: "#9ca3af", // gray-400
                borderWidth: 1,
              },
              {
                label: "Revised Cost (INR)",
                data: moduleData.map((m) => m.revisedCost),
                backgroundColor: "#60a5fa", // blue-400
                borderColor: "#3b82f6", // blue-500
                borderWidth: 1,
              },
            ],
          },
          options: {
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function (value) {
                    return value / 1000 + "k";
                  },
                },
              },
              x: {
                ticks: {
                  maxRotation: 90,
                  minRotation: 45,
                  font: { size: 10 },
                },
              },
            },
            plugins: {
              tooltip: {
                callbacks: {
                  label: function (context) {
                    return `${
                      context.dataset.label
                    }: ${context.raw.toLocaleString("en-IN")} INR`;
                  },
                },
              },
            },
          },
        });

        const allocationCtx = document
          .getElementById("allocation-chart")
          .getContext("2d");
        new Chart(allocationCtx, {
          type: "doughnut",
          data: {
            labels: moduleData.map((m) => m.title),
            datasets: [
              {
                label: "Revised Budget Allocation",
                data: moduleData.map((m) => m.revisedCost),
                backgroundColor: [
                  "#fde68a",
                  "#fcd34d",
                  "#fbbf24",
                  "#f59e0b",
                  "#a7f3d0",
                  "#6ee7b7",
                  "#34d399",
                  "#10b981",
                ],
                borderColor: "#ffffff",
                borderWidth: 2,
              },
            ],
          },
          options: {
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                callbacks: {
                  label: function (context) {
                    const total = context.chart.getDatasetMeta(0).total;
                    const percentage = ((context.raw / total) * 100).toFixed(1);
                    return `${context.label}: ${context.raw.toLocaleString(
                      "en-IN"
                    )} INR (${percentage}%)`;
                  },
                },
              },
            },
          },
        });

        // --- Smooth Scrolling & Nav Highlighting ---
        const navLinks = document.querySelectorAll(".nav-link");
        const sections = document.querySelectorAll("section");

        window.addEventListener("scroll", () => {
          let current = "";
          sections.forEach((section) => {
            const sectionTop = section.offsetTop;
            if (pageYOffset >= sectionTop - 80) {
              current = section.getAttribute("id");
            }
          });

          navLinks.forEach((link) => {
            link.classList.remove("active");
            if (link.getAttribute("href").includes(current)) {
              link.classList.add("active");
            }
          });
        });

        navLinks.forEach((link) => {
          link.addEventListener("click", function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute("href")).scrollIntoView({
              behavior: "smooth",
            });
          });
        });
      });
    </script>
  </body>
</html>
